import google.generativeai as genai
import base64
from PIL import Image
import io

# Configure the Gemini API
genai.configure(api_key="AIzaSyAqeGEgGgAwSuF-1UcimRSa64-973-5cB4")

def encode_image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def extract_from_image(image_path):
    # Initialize the model
    model = genai.GenerativeModel('gemini-pro-vision')
    
    # Load and process image
    image = Image.open(image_path)
    
    # Generate content from image
    response = model.generate_content(["Extract all text and data from this image", image])
    return response.text

def extract_from_pdf(pdf_path):
    # Convert PDF to images using pdf2image
    from pdf2image import convert_from_path
    
    images = convert_from_path(pdf_path)
    results = []
    
    # Process each page
    for i, image in enumerate(images):
        # Save image temporarily
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        # Initialize the model
        model = genai.GenerativeModel('gemini-pro-vision')
        
        # Generate content from image
        response = model.generate_content([
            "Extract all text, tables and structured data from this PDF page", 
            Image.open(img_byte_arr)
        ])
        results.append(response.text)
    
    return results

# Example usage
# image_text = extract_from_image("path/to/image.jpg")
# pdf_text = extract_from_pdf("path/to/document.pdf")

